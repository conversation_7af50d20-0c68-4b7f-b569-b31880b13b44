<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LeagueMatchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'home_team' => [
                "id" => $this->home_team_id,
                "name" => $this->homeTeam->name,
            ],
            'away_team' => [
                "id" => $this->away_team_id,
                "name" => $this->awayTeam->name,
            ],
            'week' => $this->week,
        ];
    }
}
