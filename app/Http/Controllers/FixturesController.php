<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use Illuminate\Http\Request;
use Inertia\Inertia;

class FixturesController extends Controller
{
    public function index()
    {
        $fixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->orderBy('week')
            ->get()
            ->groupBy('week');

        $fixturesByWeek = $fixtures->map(function ($weekMatches) {
            return LeagueMatchResource::collection($weekMatches);
        });

        return Inertia::render('fixtures/index', [
            'fixturesByWeek' => $fixturesByWeek
        ]);
    }

    public function startSimulation(Request $request)
    {
        // Bu kısım simülasyon başlatma işlemi için
        // Şimdilik sadece success mesajı döndürüyoruz
        return back()->with('success', 'Simülasyon başlatıldı!');
    }
}
