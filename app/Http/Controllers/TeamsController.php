<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Services\FixtureGenerator;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TeamsController extends Controller
{
    public function index()
    {
        $teams = Team::all();
        
        return Inertia::render('teams/index', [
            'teams' => $teams
        ]);
    }

    public function generateFixtures(Request $request)
    {
        $teams = Team::all();
        
        if ($teams->count() < 2) {
            return back()->with('error', 'En az 2 takım olmalı fixture oluşturmak için.');
        }

        $fixtureGenerator = new FixtureGenerator();
        $fixtures = $fixtureGenerator->generate($teams->pluck('id')->toArray());

        return redirect()->route('fixtures.index')->with('success', 'Fixture başarıyla oluşturuldu!');
    }
}
