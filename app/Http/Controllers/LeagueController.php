<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LeagueController extends Controller
{
    public function getTeams()
    {
        $teams = Team::pluck('name')->toArray();

        return response()->json([
            'data' => $teams
        ]);
    }

    public function generateFixture(FixtureGenerator $generator): JsonResponse
    {
        $seasonId = Str::random(8);
        $teamIds = Team::pluck('id')->toArray();

        $fixture = $generator->generate($teamIds, $seasonId);

        return response()->json([
            'season_id' => $seasonId,
            'data' => LeagueMatchResource::collection($fixture),
        ]);
    }

    public function generateFixtureAndRedirect(FixtureGenerator $generator)
    {
        $teamIds = Team::pluck('id')->toArray();

        if (count($teamIds) < 2) {
            return back()->with('error', 'At least 2 teams required to generate fixtures.');
        }

        $seasonId = Str::random(8);
        $fixture = $generator->generate($teamIds, $seasonId);

        // Get fixtures grouped by week
        $fixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $seasonId)
            ->orderBy('week')
            ->get()
            ->groupBy('week');

        // Calculate standings (empty at start)
        $standings = $this->calculateStandings($seasonId);

        // Calculate championship predictions
        $predictions = $this->calculateChampionshipPredictions();

        return Inertia::render('Simulation', [
            'fixturesByWeek' => $fixtures->map(function ($weekMatches) {
                return LeagueMatchResource::collection($weekMatches);
            }),
            'standings' => $standings,
            'predictions' => $predictions,
            'seasonId' => $seasonId
        ]);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'played_count' => $updateCount,
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $matches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        foreach ($matches as $match) {
            $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'message' => "Week {$nextWeek} matches played.",
            'played_count' => $matches->count(),
            'week' => $nextWeek
        ]);
    }

    // Inertia Pages
    public function teamsIndex()
    {
        $teams = Team::all();

        return Inertia::render('Teams', [
            'teams' => $teams
        ]);
    }

    public function fixturesIndex()
    {
        $fixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->orderBy('week')
            ->get()
            ->groupBy('week');

        $fixturesByWeek = $fixtures->map(function ($weekMatches) {
            return LeagueMatchResource::collection($weekMatches);
        });

        return Inertia::render('Fixtures', [
            'fixturesByWeek' => $fixturesByWeek
        ]);
    }

    public function simulationIndex()
    {
        // Get current season fixtures
        $latestSeasonId = LeagueMatches::latest()->value('season_id');

        if (!$latestSeasonId) {
            return redirect()->route('home')->with('error', 'No fixtures found. Please generate fixtures first.');
        }

        // Get fixtures grouped by week
        $fixtures = LeagueMatches::with(['homeTeam', 'awayTeam'])
            ->where('season_id', $latestSeasonId)
            ->orderBy('week')
            ->get()
            ->groupBy('week');

        // Calculate standings
        $standings = $this->calculateStandings($latestSeasonId);

        // Calculate championship predictions (simple random for now)
        $predictions = $this->calculateChampionshipPredictions();

        return Inertia::render('Simulation', [
            'fixturesByWeek' => $fixtures->map(function ($weekMatches) {
                return LeagueMatchResource::collection($weekMatches);
            }),
            'standings' => $standings,
            'predictions' => $predictions,
            'seasonId' => $latestSeasonId
        ]);
    }

    private function calculateStandings($seasonId)
    {
        $teams = Team::all();
        $standings = [];

        foreach ($teams as $team) {
            $homeMatches = LeagueMatches::where('season_id', $seasonId)
                ->where('home_team_id', $team->id)
                ->where('is_played', true)
                ->get();

            $awayMatches = LeagueMatches::where('season_id', $seasonId)
                ->where('away_team_id', $team->id)
                ->where('is_played', true)
                ->get();

            $played = $homeMatches->count() + $awayMatches->count();
            $won = 0;
            $drawn = 0;
            $lost = 0;
            $gf = 0;
            $ga = 0;

            foreach ($homeMatches as $match) {
                $gf += $match->home_score;
                $ga += $match->away_score;
                if ($match->home_score > $match->away_score) $won++;
                elseif ($match->home_score == $match->away_score) $drawn++;
                else $lost++;
            }

            foreach ($awayMatches as $match) {
                $gf += $match->away_score;
                $ga += $match->home_score;
                if ($match->away_score > $match->home_score) $won++;
                elseif ($match->away_score == $match->home_score) $drawn++;
                else $lost++;
            }

            $points = ($won * 3) + $drawn;

            $standings[] = [
                'team' => $team->name,
                'played' => $played,
                'won' => $won,
                'drawn' => $drawn,
                'lost' => $lost,
                'gf' => $gf,
                'ga' => $ga,
                'gd' => $gf - $ga,
                'points' => $points
            ];
        }

        // Sort by points, then goal difference
        usort($standings, function($a, $b) {
            if ($a['points'] == $b['points']) {
                return $b['gd'] - $a['gd'];
            }
            return $b['points'] - $a['points'];
        });

        return $standings;
    }

    private function calculateChampionshipPredictions()
    {
        $teams = Team::pluck('name')->toArray();
        $predictions = [];

        // Simple random predictions for demo
        $totalPercentage = 100;
        foreach ($teams as $index => $team) {
            if ($index == count($teams) - 1) {
                $predictions[$team] = $totalPercentage;
            } else {
                $percentage = rand(5, min(40, $totalPercentage - (count($teams) - $index - 1) * 5));
                $predictions[$team] = $percentage;
                $totalPercentage -= $percentage;
            }
        }

        return $predictions;
    }

    /**
     * @param mixed $match
     * @return bool
     */
    public function simulateMatchAsPlayed(mixed $match): bool
    {
        return $match->update([
            'home_score' => rand(0, 5),
            'away_score' => rand(0, 5),
            'is_played' => true,
            'played_at' => now(),
        ]);
    }
}
