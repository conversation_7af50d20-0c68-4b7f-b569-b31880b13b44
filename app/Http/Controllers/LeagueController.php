<?php

namespace App\Http\Controllers;

use App\Http\Resources\LeagueMatchResource;
use App\Models\LeagueMatches;
use App\Models\Team;
use App\Services\FixtureGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LeagueController extends Controller
{
    public function getTeams()
    {
        $teams = Team::pluck('name')->toArray();

        return response()->json([
            'data' => $teams
        ]);
    }

    public function generateFixture(FixtureGenerator $generator): JsonResponse
    {
        $seasonId = Str::random(8);
        $teamIds = Team::pluck('id')->toArray();

        $fixture = $generator->generate($teamIds, $seasonId);

        return response()->json([
            'season_id' => $seasonId,
            'data' => LeagueMatchResource::collection($fixture),
        ]);
    }

    public function playAllWeeks(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $matches = LeagueMatches::where('is_played', false)
            ->where('season_id', $seasonId)
            ->get();

        $updateCount = 0;
        foreach ($matches as $match) {
            $updateCount += $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'played_count' => $updateCount,
        ]);
    }

    public function playNextWeek(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'season_id' => ['required', 'string', 'size:8', 'alpha_num'],
        ]);

        $seasonId = $validated['season_id'];

        $nextWeek = LeagueMatches::where('season_id', $seasonId)
            ->where('is_played', false)
            ->min('week');

        if (is_null($nextWeek)) {
            return response()->json([
                'message' => 'All matches have already been played for this season.'
            ]);
        }

        $matches = LeagueMatches::where('season_id', $seasonId)
            ->where('week', $nextWeek)
            ->where('is_played', false)
            ->get();

        foreach ($matches as $match) {
            $this->simulateMatchAsPlayed($match);
        }

        return response()->json([
            'message' => "Week {$nextWeek} matches played.",
            'played_count' => $matches->count(),
            'week' => $nextWeek
        ]);
    }

    /**
     * @param mixed $match
     * @return bool
     */
    public function simulateMatchAsPlayed(mixed $match): bool
    {
        return $match->update([
            'home_score' => rand(0, 5),
            'away_score' => rand(0, 5),
            'is_played' => true,
            'played_at' => now(),
        ]);
    }
}
