import { Head, router } from '@inertiajs/react';

interface Team {
    id: number;
    name: string;
}

interface TeamsProps {
    teams: Team[];
}

export default function Teams({ teams }: TeamsProps) {
    const handleStartSimulation = () => {
        router.post('/start-simulation');
    };

    return (
        <>
            <Head title="Teams" />
            <div className="min-h-screen bg-gray-100 py-12">
                <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h1 className="text-2xl font-bold text-gray-900">
                                League Simulator - Teams
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                View all teams and generate fixtures
                            </p>
                        </div>

                        <div className="p-6">
                            <div className="mb-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">
                                    Teams ({teams.length})
                                </h2>

                                {teams.length === 0 ? (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">No teams found</p>
                                        <p className="text-sm text-gray-400 mt-1">
                                            At least 2 teams required to generate fixtures
                                        </p>
                                    </div>
                                ) : (
                                    <ul className="divide-y divide-gray-200">
                                        {teams.map((team, index) => (
                                            <li key={team.id} className="py-3 flex items-center">
                                                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm mr-3">
                                                    {team.name.charAt(0)}
                                                </div>
                                                <span className="text-gray-900 font-medium">{team.name}</span>
                                            </li>
                                        ))}
                                    </ul>
                                )}
                            </div>

                            <div className="flex justify-between items-center">
                                <button
                                    onClick={handleGenerateFixtures}
                                    disabled={teams.length < 2 || loading}
                                    className={`px-6 py-2 rounded-md font-medium ${
                                        teams.length < 2 || loading
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-blue-600 text-white hover:bg-blue-700'
                                    }`}
                                >
                                    {loading ? 'Generating...' : 'Generate Fixtures'}
                                </button>

                                <a
                                    href="/simulation"
                                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
                                >
                                    Start Simulation
                                </a>
                            </div>

                            {fixtureData && (
                                <div className="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
                                    <h3 className="text-lg font-medium text-green-800 mb-2">
                                        Fixtures Generated Successfully!
                                    </h3>
                                    <p className="text-sm text-green-700 mb-2">
                                        Season ID: {fixtureData.season_id}
                                    </p>
                                    <p className="text-sm text-green-700">
                                        Total Matches: {fixtureData.data.length}
                                    </p>
                                    <div className="mt-3">
                                        <a
                                            href="/simulation"
                                            className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                                        >
                                            Go to Simulation →
                                        </a>
                                    </div>
                                </div>
                            )}

                            {teams.length < 2 && (
                                <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-yellow-800">
                                                Warning
                                            </h3>
                                            <div className="mt-2 text-sm text-yellow-700">
                                                <p>
                                                    At least 2 teams are required to generate fixtures.
                                                    Please add teams to the database.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
