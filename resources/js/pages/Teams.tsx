import { Head, router } from '@inertiajs/react';

interface Team {
    id: number;
    name: string;
}

interface TeamsProps {
    teams: Team[];
}

export default function Teams({ teams }: TeamsProps) {
    const handleGenerateFixtures = () => {
        router.post('/generate-fixtures');
    };

    return (
        <>
            <Head title="Teams" />
            <div className="min-h-screen bg-gray-100 py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h1 className="text-2xl font-bold text-gray-900">
                                League Simulator - Teams
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                View all teams and generate fixtures
                            </p>
                        </div>

                        <div className="p-6">
                            <div className="mb-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">
                                    Teams ({teams.length})
                                </h2>
                                
                                {teams.length === 0 ? (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">No teams found</p>
                                        <p className="text-sm text-gray-400 mt-1">
                                            At least 2 teams required to generate fixtures
                                        </p>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {teams.map((team) => (
                                            <div
                                                key={team.id}
                                                className="bg-gray-50 rounded-lg p-4 border"
                                            >
                                                <div className="flex items-center">
                                                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                                                        {team.name.charAt(0)}
                                                    </div>
                                                    <div className="ml-3">
                                                        <h3 className="text-sm font-medium text-gray-900">
                                                            {team.name}
                                                        </h3>
                                                        <p className="text-xs text-gray-500">
                                                            Team ID: {team.id}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>

                            <div className="flex justify-between items-center">
                                <button
                                    onClick={handleGenerateFixtures}
                                    disabled={teams.length < 2}
                                    className={`px-6 py-2 rounded-md font-medium ${
                                        teams.length < 2
                                            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                            : 'bg-blue-600 text-white hover:bg-blue-700'
                                    }`}
                                >
                                    Generate Fixtures
                                </button>
                                
                                <a
                                    href="/fixtures"
                                    className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                                >
                                    View Fixtures
                                </a>
                            </div>

                            {teams.length < 2 && (
                                <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                    <div className="flex">
                                        <div className="ml-3">
                                            <h3 className="text-sm font-medium text-yellow-800">
                                                Warning
                                            </h3>
                                            <div className="mt-2 text-sm text-yellow-700">
                                                <p>
                                                    At least 2 teams are required to generate fixtures. 
                                                    Please add teams to the database.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
