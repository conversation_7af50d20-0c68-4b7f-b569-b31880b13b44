import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Calendar, Play, ArrowLeft } from 'lucide-react';

interface Team {
    id: number;
    name: string;
}

interface Match {
    home_team: Team;
    away_team: Team;
    week: number;
}

interface FixturesPageProps extends SharedData {
    fixturesByWeek: Record<string, Match[]>;
}

export default function FixturesIndex() {
    const { fixturesByWeek } = usePage<FixturesPageProps>().props;

    const handleStartSimulation = () => {
        router.post(route('fixtures.start-simulation'));
    };

    const weekNumbers = Object.keys(fixturesByWeek).sort((a, b) => parseInt(a) - parseInt(b));
    const hasFixtures = weekNumbers.length > 0;

    return (
        <>
            <Head title="Fixture'lar" />
            <div className="min-h-screen bg-gray-50 py-8 dark:bg-gray-900">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Lig Simülatörü - Fixture'lar
                        </h1>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            Haftalık maç programını görüntüleyin ve simülasyonu başlatın
                        </p>
                    </div>

                    {!hasFixtures ? (
                        <div className="text-center py-12">
                            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                                Henüz fixture oluşturulmamış
                            </h3>
                            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Fixture oluşturmak için takımlar sayfasına gidin
                            </p>
                            <div className="mt-6">
                                <Link href={route('teams.index')}>
                                    <Button>
                                        Takımlar Sayfasına Git
                                    </Button>
                                </Link>
                            </div>
                        </div>
                    ) : (
                        <>
                            <div className="mb-8 flex flex-col gap-4 sm:flex-row sm:justify-between">
                                <div className="flex items-center gap-4">
                                    <Badge variant="secondary" className="text-sm">
                                        Toplam {weekNumbers.length} hafta
                                    </Badge>
                                    <Badge variant="outline" className="text-sm">
                                        {Object.values(fixturesByWeek).flat().length} maç
                                    </Badge>
                                </div>
                                
                                <div className="flex gap-4">
                                    <Button
                                        onClick={handleStartSimulation}
                                        className="flex items-center gap-2"
                                    >
                                        <Play className="h-4 w-4" />
                                        Start Simulation
                                    </Button>
                                </div>
                            </div>

                            <div className="space-y-6">
                                {weekNumbers.map((weekNumber) => (
                                    <Card key={weekNumber}>
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                <Calendar className="h-5 w-5" />
                                                {weekNumber}. Hafta
                                            </CardTitle>
                                            <CardDescription>
                                                {fixturesByWeek[weekNumber].length} maç
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                                {fixturesByWeek[weekNumber].map((match, index) => (
                                                    <div
                                                        key={index}
                                                        className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                                                    >
                                                        <div className="flex items-center justify-between">
                                                            <div className="flex-1">
                                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                                    {match.home_team.name}
                                                                </div>
                                                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                                                    Ev Sahibi
                                                                </div>
                                                            </div>
                                                            
                                                            <div className="px-3">
                                                                <span className="text-lg font-bold text-gray-400">
                                                                    VS
                                                                </span>
                                                            </div>
                                                            
                                                            <div className="flex-1 text-right">
                                                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                                                    {match.away_team.name}
                                                                </div>
                                                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                                                    Deplasman
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </>
                    )}

                    <div className="mt-8 flex justify-start">
                        <Link href={route('teams.index')}>
                            <Button variant="ghost" className="flex items-center gap-2">
                                <ArrowLeft className="h-4 w-4" />
                                Takımlara Geri Dön
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
