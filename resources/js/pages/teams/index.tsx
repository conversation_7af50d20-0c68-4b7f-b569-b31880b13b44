import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Users, Play } from 'lucide-react';

interface Team {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
}

interface TeamsPageProps extends SharedData {
    teams: Team[];
}

export default function TeamsIndex() {
    const { teams } = usePage<TeamsPageProps>().props;

    const handleGenerateFixtures = () => {
        router.post(route('teams.generate-fixtures'));
    };

    return (
        <>
            <Head title="Takımlar" />
            <div className="min-h-screen bg-gray-50 py-8 dark:bg-gray-900">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                    <div className="mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                            Lig Simülatörü - Takımlar
                        </h1>
                        <p className="mt-2 text-gray-600 dark:text-gray-400">
                            Ligdeki takımları görüntüleyin ve fixture oluşturun
                        </p>
                    </div>

                    <div className="mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    Takımlar ({teams.length})
                                </CardTitle>
                                <CardDescription>
                                    Ligde bulunan tüm takımlar
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {teams.length === 0 ? (
                                    <div className="text-center py-8">
                                        <Users className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                                            Henüz takım yok
                                        </h3>
                                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                            Fixture oluşturmak için en az 2 takım gerekli
                                        </p>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                        {teams.map((team) => (
                                            <div
                                                key={team.id}
                                                className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                                            >
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0">
                                                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center dark:bg-blue-900">
                                                            <span className="text-sm font-medium text-blue-600 dark:text-blue-300">
                                                                {team.name.charAt(0)}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                                                            {team.name}
                                                        </h3>
                                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                                            Takım ID: {team.id}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
                        <div className="flex gap-4">
                            <Button
                                onClick={handleGenerateFixtures}
                                disabled={teams.length < 2}
                                className="flex items-center gap-2"
                            >
                                <Play className="h-4 w-4" />
                                Generate Fixtures
                            </Button>
                            
                            <Link href={route('fixtures.index')}>
                                <Button variant="outline">
                                    Fixture'ları Görüntüle
                                </Button>
                            </Link>
                        </div>

                        <Link href={route('home')}>
                            <Button variant="ghost">
                                Ana Sayfaya Dön
                            </Button>
                        </Link>
                    </div>

                    {teams.length < 2 && (
                        <div className="mt-4 rounded-md bg-yellow-50 p-4 dark:bg-yellow-900/20">
                            <div className="flex">
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                                        Uyarı
                                    </h3>
                                    <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                                        <p>
                                            Fixture oluşturmak için en az 2 takım gerekli. 
                                            Lütfen veritabanına takım ekleyin.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
