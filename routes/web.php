<?php

use App\Http\Controllers\FixturesController;
use App\Http\Controllers\TeamsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Teams routes
Route::get('/teams', [TeamsController::class, 'index'])->name('teams.index');
Route::post('/teams/generate-fixtures', [TeamsController::class, 'generateFixtures'])->name('teams.generate-fixtures');

// Fixtures routes
Route::get('/fixtures', [FixturesController::class, 'index'])->name('fixtures.index');
Route::post('/fixtures/start-simulation', [FixturesController::class, 'startSimulation'])->name('fixtures.start-simulation');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
