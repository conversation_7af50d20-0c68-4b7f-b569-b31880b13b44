<?php

use App\Http\Controllers\LeagueController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// League routes
Route::get('/', [LeagueController::class, 'teamsIndex'])->name('home');
Route::post('/generate-fixtures', [LeagueController::class, 'generateFixture'])->name('generate-fixtures');
Route::get('/fixtures', [LeagueController::class, 'fixturesIndex'])->name('fixtures');
Route::get('/simulation', [LeagueController::class, 'simulationIndex'])->name('simulation');
Route::post('/play-all-weeks', [LeagueController::class, 'playAllWeeks'])->name('play-all-weeks');
Route::post('/play-next-week', [LeagueController::class, 'playNextWeek'])->name('play-next-week');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
